import { useMemo } from "react";
import { useEmployeesQuery } from "@/hooks/tanstack-query/useEmployeesQuery";
import { usePayPeriodsQuery } from "@/hooks/tanstack-query/usePayPeriods";
import { useQuery } from "@tanstack/react-query";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import type { PayPeriod } from "@/drizzle/schema/employer/payPeriod";

export interface EmployeeListItem {
  id: string;
  name: string;
  status: "open" | "closed";
}

/**
 * Hook to fetch and filter employees relevant to a given pay period.
 */
export function useEmployeesForPeriod(
  periodId: string,
  statusFilter?: "open" | "closed",
): EmployeeListItem[] {
  const employeesData = useEmployeesQuery().data;
  const employees = useMemo(() => employeesData ?? [], [employeesData]);

  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;

  const payPeriodsData = usePayPeriodsQuery().data;
  const payPeriods = useMemo(() => payPeriodsData ?? [], [payPeriodsData]);
  const period = payPeriods.find((p: PayPeriod) => p.id === periodId);

  // Fetch payslip statuses for this period
  const { data: payslipStatuses = {} } = useQuery<Record<string, string>>({
    queryKey: ["payslipStatuses", dbPath, periodId],
    queryFn: async () => {
      if (!dbPath || !periodId) return {};
      const result = await window.api.invoke(
        "employerDb:getPayslipStatusesForPeriod",
        dbPath,
        periodId,
      );
      if (result.success) {
        // Convert array of {employee_id, status} to a map
        const statusMap: Record<string, string> = {};
        result.payslips.forEach((payslip: any) => {
          statusMap[payslip.employee_id] = payslip.status;
        });
        return statusMap;
      }
      return {};
    },
    enabled: !!dbPath && !!periodId,
    staleTime: 30 * 1000, // 30 seconds
  });

  return useMemo(() => {
    if (!period) return [];
    const periodEnd = new Date(period.period_end);

    // Helper to parse ISO (YYYY-MM-DD) or UK (DD/MM/YYYY) date strings
    const parseDate = (str: string) => {
      if (/^\d{4}-\d{2}-\d{2}/.test(str)) {
        return new Date(str);
      }
      const [day, month, year] = str.split("/").map(Number);
      return new Date(year, month - 1, day);
    };

    return employees
      .filter((emp) => {
        const start = parseDate(emp.startDate);
        const leave = emp.leaveDate ? parseDate(emp.leaveDate) : null;
        // Exclude if hired after period end or left before this period starts
        const started = start <= periodEnd;
        const scheduleId = period.schedule_id;
        const prevPeriod = payPeriods.find(
          (p) =>
            p.schedule_id === scheduleId &&
            p.period_number === period.period_number - 1 &&
            p.tax_year === period.tax_year,
        );
        const prevEnd = prevPeriod ? parseDate(prevPeriod.period_end) : null;
        const leftBefore = leave && prevEnd ? leave <= prevEnd : false;
        if (!started || leftBefore) return false;
        // Normalize payment frequency to match period.type
        const freqKey =
          emp.paymentFrequency
            ?.trim()
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, "_") || "";
        const freqMap: Record<string, string> = {
          weekly: "weekly",
          "2_weekly": "two_weekly",
          two_weekly: "two_weekly",
          "4_weekly": "four_weekly",
          four_weekly: "four_weekly",
          monthly: "monthly",
          quarterly: "quarterly",
          yearly: "yearly",
        };
        const empType = freqMap[freqKey] || "";
        return empType === period.type;
      })
      .map((emp) => ({
        id: emp.id,
        name: `${emp.firstName} ${emp.lastName}`,
        status: (payslipStatuses[emp.id] || "open") as "open" | "closed",
      }))
      .filter((emp) => !statusFilter || emp.status === statusFilter);
  }, [employees, period, payslipStatuses, payPeriods, statusFilter]);
}
